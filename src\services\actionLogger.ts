import { collection, addDoc, serverTimestamp } from 'firebase/firestore';
import { db } from '../config/firebase';
import type { ActionType } from '../types/actionHistory';
import { useAuthStore } from '../stores/auth';

const logAction = async (action: ActionType, details: Record<string, any>) => {
  const authStore = useAuthStore();
  const user = authStore.user;

  if (!user) {
    console.warn('Cannot log action, user not authenticated');
    return;
  }

  try {
    await addDoc(collection(db, 'actionHistory'), {
      action,
      userId: user.id,
      username: user.username,
      timestamp: serverTimestamp(),
      details,
    });
  } catch (error) {
    console.error('Error logging action:', error);
  }
};

export default logAction;
