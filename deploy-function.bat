@echo off
REM Ultra-fast Cloud Function deployment for server code changes

set PROJECT_ID=textbook-platform
set FUNCTION_NAME=textbook-api
set REGION=us-central1

echo ⚡ Deploying server as Cloud Function (Ultra Fast Mode)...

REM Deploy as Cloud Function (no container build needed)
gcloud functions deploy %FUNCTION_NAME% ^
    --gen2 ^
    --runtime nodejs18 ^
    --source . ^
    --entry-point app ^
    --trigger-http ^
    --allow-unauthenticated ^
    --region %REGION% ^
    --memory 1Gi ^
    --set-env-vars NODE_ENV=production

echo ✅ Function deployment complete!
echo.
echo 🌐 Your function URL is:
gcloud functions describe %FUNCTION_NAME% --region=%REGION% --format="value(serviceConfig.uri)"
