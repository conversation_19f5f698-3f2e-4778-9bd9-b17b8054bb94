@echo off
REM Fast server-only deployment script for textbook-platform

REM Configuration
set PROJECT_ID=textbook-platform
set SERVICE_NAME=my-textbook-app
set REGION=us-central1

echo 🚀 Deploying server-only to Google Cloud Run (Fast Mode)...

REM Check if gcloud is installed
where gcloud >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Google Cloud CLI is not installed. Please install it first:
    echo    https://cloud.google.com/sdk/docs/install
    exit /b 1
)

REM Set the project
echo 📋 Setting project to %PROJECT_ID%...
gcloud config set project %PROJECT_ID%

REM Build and deploy using server-only Dockerfile
echo 🏗️  Building and deploying server-only container...
gcloud run deploy %SERVICE_NAME% ^
    --source . ^
    --dockerfile Dockerfile.server ^
    --platform managed ^
    --region %REGION% ^
    --allow-unauthenticated ^
    --port 8080 ^
    --memory 1Gi ^
    --cpu 1 ^
    --max-instances 10 ^
    --set-env-vars NODE_ENV=production

if %ERRORLEVEL% EQU 0 (
    echo ✅ Server deployment complete!
    echo.
    echo 🔄 Waiting for service to be ready...
    timeout /t 10 /nobreak >nul

    echo 🌐 Your production URL is:
    gcloud run services list --filter="metadata.name=%SERVICE_NAME%" --format="value(status.url)"
    echo.

    echo 🧪 Testing new endpoint...
    for /f %%i in ('gcloud run services list --filter="metadata.name=%SERVICE_NAME%" --format="value(status.url)"') do (
        curl -s "%%i/api/user/profile" -H "Authorization: Bearer test" | findstr "profile\|error\|Cannot" >nul
        if !ERRORLEVEL! EQU 0 (
            echo ✅ New endpoint is responding!
        ) else (
            echo ⚠️  Endpoint might still be deploying...
        )
    )
    echo.
    echo 📋 You can also monitor at: https://console.cloud.google.com/run
) else (
    echo ❌ Deployment failed!
    exit /b 1
)
