import { defineStore } from 'pinia';
import type { Book } from '../types/book';
import type { Item } from '../types/item';
import { useAuthStore } from './auth';
import { collection, onSnapshot, getDocs } from 'firebase/firestore';
import { db } from '../config/firebase';
import { buildApiUrl } from '../config/api';

import logAction from '../services/actionLogger';

interface BookState {
  books: Book[];
  selectedBookId: string | null;
  loading: boolean;
  error: string | null;
  isRealTimeEnabled: boolean;
  unsubscribers: (() => void)[];
  bookListeners: Map<string, (() => void)[]>; // Track listeners per book
  chapterItemListeners: Map<string, (() => void)>; // Track item listeners per chapter (key: bookId_chapterId)
  isLoadingFromFirestore: boolean; // Prevent duplicate loading calls
  // Loading states for individual actions
  actionLoading: {
    addingBook: boolean;
    addingChapter: boolean;
    addingItem: boolean;
    updatingItem: boolean;
    deletingItem: boolean;
    deletingChapter: boolean;
    deletingBook: boolean;
  };
}

export const useBookStore = defineStore('book', {
  state: (): BookState => ({
    books: [],
    selectedBookId: null,
    loading: false,
    error: null,
    isRealTimeEnabled: false,
    unsubscribers: [],
    bookListeners: new Map(),
    chapterItemListeners: new Map(),
    isLoadingFromFirestore: false,
    actionLoading: {
      addingBook: false,
      addingChapter: false,
      addingItem: false,
      updatingItem: false,
      deletingItem: false,
      deletingChapter: false,
      deletingBook: false,
    },
  }),

  getters: {
    selectedBook: (state) => {
      return state.books.find(book => book.id === state.selectedBookId) || null;
    },
    getBook: (state) => (id: string) => {
      return state.books.find(book => book.id === id) || null;
    },
  },

  actions: {
    // Disable real-time listeners
    disableRealTimeUpdates() {
      console.log('🔇 Disabling real-time updates...');

      // Clean up main unsubscribers
      this.unsubscribers.forEach(unsubscribe => unsubscribe());
      this.unsubscribers = [];

      // Clean up book-specific listeners
      for (const [bookId, listeners] of this.bookListeners.entries()) {
        console.log(`🧹 Cleaning up listeners for book ${bookId}`);
        listeners.forEach(unsub => unsub());
      }
      this.bookListeners.clear();

      this.isRealTimeEnabled = false;
    },

    // Clean up listeners
    cleanup() {
      console.log('🧹 Cleaning up book store...');
      this.disableRealTimeUpdates();
      // Also clear all book-specific listeners
      for (const bookId of this.bookListeners.keys()) {
        this.cleanupBookListeners(bookId);
      }
      // Clear all chapter item listeners
      for (const key of this.chapterItemListeners.keys()) {
        this.cleanupChapterItemsListenerByKey(key);
      }
      // Reset loading flag
      this.isLoadingFromFirestore = false;
    },

    // Clean up listeners for a specific book
    cleanupBookListeners(bookId: string) {
      const listeners = this.bookListeners.get(bookId);
      if (listeners) {
        console.log(`🧹 Cleaning up listeners for book ${bookId}`);
        listeners.forEach(unsub => unsub());
        this.bookListeners.delete(bookId);
      }
      // Also clean up all item listeners associated with this book's chapters
      for (const key of this.chapterItemListeners.keys()) {
        if (key.startsWith(`${bookId}_`)) {
          this.cleanupChapterItemsListenerByKey(key);
        }
      }
    },

    // Clean up a specific chapter's item listener by key
    cleanupChapterItemsListenerByKey(key: string) {
      const unsub = this.chapterItemListeners.get(key);
      if (unsub) {
        unsub();
        this.chapterItemListeners.delete(key);
      }
    },

    // Initialize the store - non-blocking approach
    async initializeStore() {
      console.log('🚀 [BookStore] initializeStore: Entry');
      const authStore = useAuthStore();

      // Don't trigger loading here - let the auth state change handler deal with all loading
      // This prevents duplicate loading when both initializeStore and onAuthStateChange are called
      if (authStore.initialized) {
        console.log('📚 [BookStore] Auth already initialized, but skipping manual trigger to prevent duplicate loading');
        console.log('📚 [BookStore] Auth state change handler will handle loading when needed');
      } else {
        console.log('⏳ [BookStore] Auth not ready yet, will load data when auth state changes');
      }
      console.log('🚀 [BookStore] initializeStore: Exit');
    },

    // Called when user authentication state changes
    async onAuthStateChange(isAuthenticated: boolean) {
      console.log(`🔐 [BookStore] onAuthStateChange: ${isAuthenticated ? 'authenticated' : 'not authenticated'}`);

      if (isAuthenticated) {
        // User signed in - load books from Firestore
        console.log('📚 [BookStore] User signed in, setting up real-time book listeners...');

        // Prevent duplicate loading if already in progress
        if (this.isLoadingFromFirestore) {
          console.log('⚠️ [BookStore] Already loading from Firestore, skipping duplicate call');
          return;
        }

        // Clean up any existing listeners first
        this.cleanup();
        // The onSnapshot listener within loadBooksFromFirestore will handle initial data loading
        this.loadBooksFromFirestore();
      } else {
        // User signed out - clear books and load static fallback
        console.log('🔒 [BookStore] User signed out, clearing books and loading static fallback...');
        this.cleanup();
        await this.loadBooksFromStatic();
      }
      console.log(`🔐 [BookStore] onAuthStateChange: Exit (isAuthenticated: ${isAuthenticated})`);
    },

    // Load books directly from Firestore with real-time updates (optimized)
    async loadBooksFromFirestore() {
      console.log('📚 [BookStore] loadBooksFromFirestore: Entry');

      // Prevent duplicate calls
      if (this.isLoadingFromFirestore) {
        console.log('⚠️ [BookStore] Already loading from Firestore, skipping duplicate call');
        return;
      }

      this.isLoadingFromFirestore = true;
      this.loading = true;
      this.error = null;

      try {
        // Set up real-time listener for books collection
        const booksCollection = collection(db, 'books');

        // Use onSnapshot for real-time updates
        const unsubscribe = onSnapshot(booksCollection, (snapshot) => {
          console.log('📡 [BookStore] onSnapshot callback: Books collection changed.');
          console.log('📡 [BookStore] Current books array BEFORE processing:', this.books.map(b => b.title));
          snapshot.docChanges().forEach((change) => {
            const bookData = change.doc.data();
            const bookId = change.doc.id;
            const existingBookIndex = this.books.findIndex(b => b.id === bookId);

            console.log(`  Change Type: ${change.type}, Book ID: ${bookId}, Title: ${bookData.title}`);

            if (change.type === 'added') {
              console.log(`➕ Added book: ${bookData.title}`);
              const newBook: Book = {
                id: bookId,
                title: bookData.title || '',
                description: bookData.description || '',
                chapters: []
              };
              this.books.push(newBook);
              this.setupBookListeners(bookId);
            } else if (change.type === 'modified') {
              console.log(`✏️ Modified book: ${bookData.title}`);
              if (existingBookIndex !== -1) {
                this.books[existingBookIndex] = {
                  ...this.books[existingBookIndex],
                  title: bookData.title || '',
                  description: bookData.description || '',
                };
              }
            } else if (change.type === 'removed') {
              console.log(`🗑️ Removed book: ${bookData.title}`);
              if (existingBookIndex !== -1) {
                this.books.splice(existingBookIndex, 1);
                this.cleanupBookListeners(bookId);
                // Clear selected book if it was the one deleted
                if (this.selectedBookId === bookId) {
                  this.selectedBookId = null;
                }
              }
            }
          });

          // Ensure reactivity for the books array if direct push/splice isn't enough
          this.books = [...this.books];
          this.loading = false;
          this.isLoadingFromFirestore = false; // Reset flag on successful load
          console.log(`✅ [BookStore] Real-time update complete. Total books: ${this.books.length}`);
          console.log('✅ [BookStore] Current books array AFTER processing:', this.books.map(b => b.title));
        }, (error) => {
          console.error('❌ [BookStore] Firestore listener error:', error);
          this.error = error.message;
          this.loading = false;
          this.isLoadingFromFirestore = false; // Reset flag on error
          // Fall back to static file if Firestore fails
          this.loadBooksFromStatic();
        });

        // Store the unsubscriber
        this.unsubscribers.push(unsubscribe);

        // The onSnapshot listener will handle initial data loading

      } catch (error) {
        console.error('❌ Failed to set up Firestore listener:', error);
        this.isLoadingFromFirestore = false; // Reset flag on error
        // Fall back to static file
        this.loadBooksFromStatic();
      }
    },

    

    // Set up real-time listeners for a specific book's chapters and items
    setupBookListeners(bookId: string) {
      console.log(`📑 Setting up listeners for book ${bookId}...`);

      const bookListeners: (() => void)[] = [];

      try {
        // Set up real-time listener for chapters
        const chaptersRef = collection(db, 'books', bookId, 'chapters');

        const unsubscribeChapters = onSnapshot(chaptersRef, (chaptersSnapshot) => {
          console.log(`📡 Chapters changed for book ${bookId} - ${chaptersSnapshot.docs.length} chapters found`);

          const book = this.books.find(b => b.id === bookId);
          if (!book) {
            console.warn(`❌ Book ${bookId} not found in state during chapter update!`);
            return;
          }

          // Create a temporary array for new chapters to maintain reactivity
          const newChapters: Book['chapters'] = [];
          const processedChapterIds = new Set<string>();

          chaptersSnapshot.docChanges().forEach((change) => {
            const chapterData = change.doc.data();
            const chapterId = change.doc.id;
            processedChapterIds.add(chapterId);

            if (change.type === 'added') {
              console.log(`➕ Added chapter: ${chapterData.title}`);
              const newChapter = {
                id: chapterId,
                title: chapterData.title || '',
                items: [] as Item[] // Initialize items as empty, will be populated by item listener
              };
              newChapters.push(newChapter);
              this.setupChapterItemsListener(bookId, chapterId, newChapter.items);
            } else if (change.type === 'modified') {
              console.log(`✏️ Modified chapter: ${chapterData.title}`);
              // Find and update the existing chapter in the book's chapters array
              const existingChapter = book.chapters.find(c => c.id === chapterId);
              if (existingChapter) {
                existingChapter.title = chapterData.title || '';
                // No need to re-setup item listener unless chapter ID changes (which it shouldn't)
              }
            } else if (change.type === 'removed') {
              console.log(`🗑️ Removed chapter: ${chapterData.title}`);
              // Clean up item listener for the removed chapter
              this.cleanupChapterItemsListenerByKey(`${bookId}_${chapterId}`);
              // The chapter will be removed from book.chapters when newChapters replaces it
            }
          });

          // Filter out removed chapters from the existing book.chapters array
          book.chapters = book.chapters.filter(c => processedChapterIds.has(c.id));

          // Add or update chapters from newChapters into book.chapters
          newChapters.forEach(newCh => {
            const existingIndex = book.chapters.findIndex(c => c.id === newCh.id);
            if (existingIndex === -1) {
              book.chapters.push(newCh);
            } else {
              // Update existing chapter (title might have changed)
              book.chapters[existingIndex].title = newCh.title;
            }
          });

          // Sort chapters by order if available
          book.chapters.sort((a, b) => {
            const aOrder = chaptersSnapshot.docs.find(doc => doc.id === a.id)?.data()?.order || 0;
            const bOrder = chaptersSnapshot.docs.find(doc => doc.id === b.id)?.data()?.order || 0;
            return aOrder - bOrder;
          });

          // Trigger reactivity for the book's chapters array
          book.chapters = [...book.chapters];

          console.log(`✅ Updated ${book.chapters.length} chapters for book ${book.title}`);

        }, (error) => {
          console.error(`❌ Chapters listener error for book ${bookId}:`, error);
        });

        bookListeners.push(unsubscribeChapters);
        this.bookListeners.set(bookId, bookListeners);

        console.log(`✅ Listeners set up for book ${bookId}`);

      } catch (error) {
        console.error(`❌ Failed to set up listeners for book ${bookId}:`, error);
      }
    },

    // Set up real-time listener for a specific chapter's items
    setupChapterItemsListener(bookId: string, chapterId: string, itemsArray: Item[]) {
      console.log(`📑 Setting up items listener for chapter ${chapterId} in book ${bookId}...`);

      const itemsRef = collection(db, 'books', bookId, 'chapters', chapterId, 'items');
      const listenerKey = `${bookId}_${chapterId}`;

      // Clean up existing listener for this chapter's items if any
      this.cleanupChapterItemsListenerByKey(listenerKey);

      const unsubscribeItems = onSnapshot(itemsRef, (itemsSnapshot) => {
        console.log(`📡 Items changed for chapter ${chapterId} - ${itemsSnapshot.docs.length} items found`);

        const newItems: Item[] = [];
        itemsSnapshot.docChanges().forEach((change) => {
          const itemData = change.doc.data();
          const itemId = change.doc.id;

          if (change.type === 'added') {
            console.log(`➕ Added item: ${itemData.title || itemData.question || 'Untitled'}`);
            newItems.push({
              id: itemId,
              ...itemData
            } as Item);
          } else if (change.type === 'modified') {
            console.log(`✏️ Modified item: ${itemData.title || itemData.question || 'Untitled'}`);
            const existingItemIndex = itemsArray.findIndex(i => i.id === itemId);
            if (existingItemIndex !== -1) {
              itemsArray[existingItemIndex] = {
                ...itemsArray[existingItemIndex],
                ...itemData
              };
            }
          } else if (change.type === 'removed') {
            console.log(`🗑️ Removed item: ${itemData.title || itemData.question || 'Untitled'}`);
            const existingItemIndex = itemsArray.findIndex(i => i.id === itemId);
            if (existingItemIndex !== -1) {
              itemsArray.splice(existingItemIndex, 1);
            }
          }
        });

        // Sort items by order if available (after all changes)
        newItems.sort((a, b) => {
          const aOrder = itemsSnapshot.docs.find(doc => doc.id === a.id)?.data()?.order || 0;
          const bOrder = itemsSnapshot.docs.find(doc => doc.id === b.id)?.data()?.order || 0;
          return aOrder - bOrder;
        });

        // Update the chapter's items array reactively
        itemsArray.splice(0, itemsArray.length, ...newItems);

        console.log(`✅ Updated ${itemsArray.length} items for chapter ${chapterId}`);

      }, (error) => {
        console.error(`❌ Items listener error for chapter ${chapterId}:`, error);
      });

      // Store the unsubscriber for this chapter's items
      this.chapterItemListeners.set(listenerKey, unsubscribeItems);
    },

    

    // Fallback static loading method
    async loadChaptersAndItemsStatic(books: Book[]) {
      console.log('📑 Loading chapters and items statically (fallback)...');

      try {
        for (const book of books) {
          const bookRef = collection(db, 'books', book.id, 'chapters');
          const chaptersSnapshot = await getDocs(bookRef);

          for (const chapterDoc of chaptersSnapshot.docs) {
            const chapterData = chapterDoc.data();
            const chapter = {
              id: chapterDoc.id,
              title: chapterData.title || '',
              items: [] as Item[]
            };

            // Load items for this chapter
            const itemsRef = collection(db, 'books', book.id, 'chapters', chapter.id, 'items');
            const itemsSnapshot = await getDocs(itemsRef);

            for (const itemDoc of itemsSnapshot.docs) {
              const itemData = itemDoc.data();
              chapter.items.push({
                id: itemDoc.id,
                ...itemData
              } as Item);
            }

            book.chapters.push(chapter);
          }
        }

        // Update the store with complete data
        this.books = [...books];
        console.log(`✅ Static loading complete - loaded chapters and items`);

      } catch (error) {
        console.error('❌ Failed to load chapters/items statically:', error);
      }
    },

    // Fallback method for unauthenticated users
    async loadBooksFromStatic() {
      console.log('📄 Setting empty books for unauthenticated user...');
      this.books = [];
      this.loading = false;
      this.error = null;
      console.log('✅ Set empty books array for unauthenticated user');
    },

    // Book selection methods
    selectBook(bookId: string | null) {
      this.selectedBookId = bookId;
    },

    // Add book method
    async addBook(book: { title: string; description: string }) {
      this.actionLoading.addingBook = true;
      try {
        const authStore = useAuthStore();
        const headers = await authStore.getAuthHeaders();
        const response = await fetch(buildApiUrl('/api/books'), {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...headers,
          },
          body: JSON.stringify(book),
        });

        if (!response.ok) {
          throw new Error(`Failed to add book: ${response.status}`);
        }

        const newBook = await response.json();
        logAction('create-book', { bookId: newBook.id, bookTitle: book.title });
        // Don't manually add to local state - let the real-time listener handle it
        return newBook;
      } catch (error) {
        console.error('Error adding book:', error);
        // Fallback to local state only
        const bookCount = this.books.length + 1;
        const fallbackBook = {
          id: `book${bookCount}`,
          title: book.title,
          description: book.description,
          chapters: []
        };
        this.books.push(fallbackBook);
        return fallbackBook;
      } finally {
        this.actionLoading.addingBook = false;
      }
    },

    // Add chapter method
    async addChapter(bookId: string, chapter: { title: string }) {
      this.actionLoading.addingChapter = true;
      try {
        const authStore = useAuthStore();
        const headers = await authStore.getAuthHeaders();
        const response = await fetch(buildApiUrl(`/api/books/${bookId}/chapters`), {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...headers,
          },
          body: JSON.stringify(chapter),
        });

        if (!response.ok) {
          throw new Error(`Failed to add chapter: ${response.status}`);
        }

        const newChapter = await response.json();
        logAction('create-chapter', { bookId, chapterId: newChapter.id, chapterTitle: chapter.title });
        // Don't manually add to local state - let the real-time listener handle it
        return newChapter;
      } catch (error) {
        console.error('Error adding chapter:', error);
        // Fallback to local state only
        const book = this.books.find(b => b.id === bookId);
        if (book) {
          const chapterCount = book.chapters.length + 1;
          const fallbackChapter = {
            id: `chapter${chapterCount}`,
            title: chapter.title,
            items: []
          };
          book.chapters.push(fallbackChapter);
          return fallbackChapter;
        }
        throw error;
      } finally {
        this.actionLoading.addingChapter = false;
      }
    },

    // Add item method
    async addItem(bookId: string, chapterId: string, item: Partial<Item>) {
      this.actionLoading.addingItem = true;
      console.log(`🔧 Adding item to book ${bookId}, chapter ${chapterId}:`, item);

      // Check if listeners are set up for this book
      if (this.bookListeners.has(bookId)) {
        console.log(`✅ Real-time listeners are active for book ${bookId}`);
      } else {
        console.warn(`❌ No real-time listeners found for book ${bookId}! Available listeners:`, Array.from(this.bookListeners.keys()));
      }

      try {
        const authStore = useAuthStore();
        const headers = await authStore.getAuthHeaders();
        const response = await fetch(buildApiUrl(`/api/books/${bookId}/chapters/${chapterId}/items`), {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...headers,
          },
          body: JSON.stringify(item),
        });

        if (!response.ok) {
          throw new Error(`Failed to add item: ${response.status}`);
        }

        const newItem = await response.json();
        logAction('create-item', { bookId, chapterId, itemId: newItem.id, itemTitle: item.title || '' });
        console.log('✅ API successfully created item:', newItem);
        console.log('🔄 Waiting for real-time listener to pick up the new item...');

        // Don't manually add to local state - let the real-time listener handle it
        return newItem;
      } catch (error) {
        console.error('Error adding item:', error);
        // Fallback to local state only
        const book = this.books.find(b => b.id === bookId);
        const chapter = book?.chapters.find(c => c.id === chapterId);
        if (chapter) {
          const itemCount = chapter.items.length + 1;
          const fallbackItem: Item = {
            id: `item${itemCount}`,
            title: item.title || 'Untitled Item',
            type: item.type || 'text',
            question: item.question,
            options: item.options,
            correctAnswer: item.correctAnswer,
            timedAnswer: item.timedAnswer,
            revealTimeSeconds: item.revealTimeSeconds,
            testQuestions: item.testQuestions,
          };
          chapter.items.push(fallbackItem);
          return fallbackItem;
        }
        throw error;
      } finally {
        this.actionLoading.addingItem = false;
      }
    },

    // Update item method
    async updateItem(bookId: string, chapterId: string, itemId: string, item: Partial<Item>) {
      this.actionLoading.updatingItem = true;
      try {
        const authStore = useAuthStore();
        const headers = await authStore.getAuthHeaders();
        const response = await fetch(buildApiUrl(`/api/books/${bookId}/chapters/${chapterId}/items/${itemId}`), {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            ...headers,
          },
          body: JSON.stringify(item),
        });

        if (!response.ok) {
          throw new Error(`Failed to update item: ${response.status}`);
        }

        const updatedItem = await response.json();
        logAction('update-item', { bookId, chapterId, itemId, itemTitle: item.title || '' });
        // Don't manually update local state - let the real-time listener handle it
        return updatedItem;
      } catch (error) {
        console.error('Error updating item:', error);
        // Fallback to local state only
        const book = this.books.find(b => b.id === bookId);
        const chapter = book?.chapters.find(c => c.id === chapterId);
        if (chapter) {
          const itemIndex = chapter.items.findIndex(i => i.id === itemId);
          if (itemIndex !== -1) {
            chapter.items[itemIndex] = { ...chapter.items[itemIndex], ...item };
          }
        }
        throw error;
      } finally {
        this.actionLoading.updatingItem = false;
      }
    },

    // Delete item method
    async deleteItem(bookId: string, chapterId: string, itemId: string) {
      this.actionLoading.deletingItem = true;
      const book = this.books.find(b => b.id === bookId);
      const chapter = book?.chapters.find(c => c.id === chapterId);
      const deletedItem = chapter?.items.find(i => i.id === itemId);
      const itemTitle = deletedItem?.title || 'Unknown Item';

      try {
        const authStore = useAuthStore();
        const headers = await authStore.getAuthHeaders();
        const response = await fetch(buildApiUrl(`/api/books/${bookId}/chapters/${chapterId}/items/${itemId}`), {
          method: 'DELETE',
          headers: headers,
        });

        if (!response.ok) {
          throw new Error(`Failed to delete item: ${response.status}`);
        }

        logAction('delete-item', { bookId, chapterId, itemId, itemTitle });
        // Don't manually update local state - let the real-time listener handle it
      } catch (error) {
        console.error('Error deleting item:', error);
        throw error;
      } finally {
        this.actionLoading.deletingItem = false;
      }
    },

    // Delete chapter method
    async deleteChapter(bookId: string, chapterId: string) {
      this.actionLoading.deletingChapter = true;
      const book = this.books.find(b => b.id === bookId);
      const deletedChapter = book?.chapters.find(c => c.id === chapterId);
      const chapterTitle = deletedChapter?.title || 'Unknown Chapter';

      try {
        const authStore = useAuthStore();
        const headers = await authStore.getAuthHeaders();
        const response = await fetch(buildApiUrl(`/api/books/${bookId}/chapters/${chapterId}`), {
          method: 'DELETE',
          headers: headers,
        });

        if (!response.ok) {
          throw new Error(`Failed to delete chapter: ${response.status}`);
        }

        logAction('delete-chapter', { bookId, chapterId, chapterTitle });
        // Don't manually update local state - let the real-time listener handle it
      } catch (error) {
        console.error('Error deleting chapter:', error);
        throw error;
      } finally {
        this.actionLoading.deletingChapter = false;
      }
    },

    // Delete book method
    async deleteBook(bookId: string) {
      this.actionLoading.deletingBook = true;
      const deletedBook = this.books.find(book => book.id === bookId);
      const bookTitle = deletedBook?.title || 'Unknown Book';

      try {
        const authStore = useAuthStore();
        const headers = await authStore.getAuthHeaders();
        const response = await fetch(buildApiUrl(`/api/books/${bookId}`), {
          method: 'DELETE',
          headers: headers,
        });

        if (!response.ok) {
          throw new Error(`Failed to delete book: ${response.status}`);
        }

        logAction('delete-book', { bookId, bookTitle });
        // Don't manually update local state - let the real-time listener handle it
        // But clear selection if deleted book was selected
        if (this.selectedBookId === bookId) {
          this.selectedBookId = null;
        }
      } catch (error) {
        console.error('Error deleting book:', error);
        throw error;
      } finally {
        this.actionLoading.deletingBook = false;
      }
    },
  }
});
