@echo off
REM Google Cloud deployment script for textbook-platform

REM Configuration
set PROJECT_ID=textbook-platform
set SERVICE_NAME=textbook-platform
set REGION=us-central1

echo 🚀 Deploying textbook-platform to Google Cloud Run...

REM Check if gcloud is installed
where gcloud >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Google Cloud CLI is not installed. Please install it first:
    echo    https://cloud.google.com/sdk/docs/install
    exit /b 1
)

REM Set the project
echo 📋 Setting project to %PROJECT_ID%...
gcloud config set project %PROJECT_ID%

REM Enable required APIs
echo 🔧 Enabling required APIs...
gcloud services enable cloudbuild.googleapis.com || echo "Failed to enable Cloud Build API"
gcloud services enable run.googleapis.com || echo "Failed to enable Cloud Run API"
gcloud services enable firestore.googleapis.com || echo "Failed to enable Firestore API"
echo APIs enabled successfully

REM Build and deploy
echo 🏗️  Building and deploying to Cloud Run...
gcloud run deploy %SERVICE_NAME% ^
    --source . ^
    --platform managed ^
    --region %REGION% ^
    --allow-unauthenticated ^
    --port 3001 ^
    --memory 1Gi ^
    --cpu 1 ^
    --max-instances 10 ^
    --set-env-vars NODE_ENV=production

echo ✅ Deployment complete!
echo.
echo 🌐 Your production URL is:
gcloud run services list --filter="metadata.name=%SERVICE_NAME%" --format="value(status.url)"
echo.
echo 📋 You can also find it at: https://console.cloud.google.com/run
