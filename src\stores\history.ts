import { defineStore } from 'pinia';
import { collection, onSnapshot, query, orderBy, getDocs, deleteDoc, doc } from 'firebase/firestore';
import { db } from '../config/firebase';
import type { ActionHistory } from '../types/actionHistory';
import { useAuthStore } from './auth';

interface HistoryState {
  history: ActionHistory[];
  loading: boolean;
  error: string | null;
  unsubscribe: (() => void) | null;
  newActionsAvailable: boolean;
  lastSeenCount: number;
}

export const useHistoryStore = defineStore('history', {
  state: (): HistoryState => ({
    history: [],
    loading: false,
    error: null,
    unsubscribe: null,
    newActionsAvailable: false,
    lastSeenCount: 0,
  }),

  actions: {
    subscribeToHistory() {
      if (this.unsubscribe) return;

      this.loading = true;
      const historyCollection = collection(db, 'actionHistory');
      const q = query(historyCollection, orderBy('timestamp', 'desc'));

      const authStore = useAuthStore();

      this.unsubscribe = onSnapshot(q, (snapshot) => {
        console.log(`[HistoryStore] onSnapshot: ${snapshot.docs.length} documents received.`);
        console.log(`[HistoryStore] Current user ID: ${authStore.user?.id}`);
        const newHistory = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
        })) as ActionHistory[];

        // Check for new actions not performed by the current user
        if (this.history.length > 0) {
          const currentUserId = authStore.user?.id;
          const unseenActions = newHistory.filter(action => 
            !this.history.some(oldAction => oldAction.id === action.id) && 
            action.userId !== currentUserId
          );
          if (unseenActions.length > 0) {
            this.newActionsAvailable = true;
          }
        }

        this.history = newHistory;
        this.loading = false;
      }, (error) => {
        this.error = error.message;
        this.loading = false;
      });
    },

    markHistoryAsSeen() {
      this.newActionsAvailable = false;
      this.lastSeenCount = this.history.length;
    },

    unsubscribeFromHistory() {
      if (this.unsubscribe) {
        this.unsubscribe();
        this.unsubscribe = null;
      }
    },

    async clearAllHistory() {
      this.loading = true;
      try {
        const historyCollection = collection(db, 'actionHistory');
        const snapshot = await getDocs(historyCollection);
        const deletePromises = snapshot.docs.map(d => deleteDoc(doc(db, 'actionHistory', d.id)));
        await Promise.all(deletePromises);
        this.history = []; // Clear local state immediately
        this.newActionsAvailable = false;
        this.lastSeenCount = 0;
      } catch (error) {
        console.error('Error clearing history:', error);
        this.error = 'Failed to clear history.';
      } finally {
        this.loading = false;
      }
    },
  },
});
