<template>
  <div class="flex flex-col h-full">
    <!-- Main Content Area -->
    <div class="flex-1 overflow-y-auto">
      <Button
        v-if="authStore.canCreateBooks"
        label="Create Book"
        icon="pi pi-plus"
        class="mb-4 w-full"
        :loading="store.actionLoading.addingBook"
        @click="openAddBookDialog"
      />

      <div v-if="store.loading" class="space-y-2">
        <!-- Loading skeleton for books -->
        <div class="flex items-center gap-2 text-surface-500 text-sm p-3 mb-3">
          <i class="pi pi-spin pi-spinner"></i>
          <span>Loading books...</span>
        </div>
        <!-- Skeleton items -->
        <div v-for="i in 3" :key="i" class="animate-pulse">
          <div class="flex items-center p-3 rounded-lg mb-1">
            <div class="w-4 h-4 bg-surface-300 rounded mr-3"></div>
            <div class="h-4 bg-surface-300 rounded flex-1"></div>
          </div>
        </div>
      </div>
      <div v-else-if="store.error" class="text-red-500 text-sm p-3">Error: {{ store.error }}</div>
      <div v-else-if="!store.books.length" class="text-surface-500 text-sm p-3">
        {{ authStore.isAuthenticated ? 'No books available' : 'Please sign in to view books' }}
      </div>
      <Menu v-else :model="menuItems" class="w-full !border-surface-300 !rounded-xl">
        <template #submenuheader="{item}">
          <span class="font-semibold text-primary">{{ item.label }}</span>
          
        </template>
        <template #item="{ item, props }">
          <a
            v-if="item.id"
            v-bind="props.action"
            :class="[
              'flex items-center p-3 cursor-pointer rounded-lg transition-colors duration-200 mb-1',
              store.selectedBookId === item.id
                ? 'bg-blue-100 text-blue-700 font-medium'
                : 'hover:bg-surface-100 text-surface-700'
            ]"
            @click="selectBook(item.id)"
          >
            <i class="pi pi-book mr-3 text-sm"></i>
            <span class="text-sm">{{ item.label }}</span>
          </a>
        </template>
      </Menu>
    </div>

    <!-- User Section at Bottom -->
    <div class="border-t border-surface-300 pt-4 mt-4">
      <!-- Real-time Status temporarily disabled due to compilation issues -->

      <div class="mb-3">
        <div class="flex items-center gap-2 mb-2">
          <i class="pi pi-user text-surface-500 text-sm"></i>
          <span class="text-sm font-medium text-surface-700">{{ authStore.user?.username }}</span>
        </div>
        <span class="inline-block text-xs px-2 py-1 bg-blue-100 text-blue-700 rounded-full font-medium">
          {{ authStore.user?.role }}
        </span>
      </div>

      <!-- User Management Button (Admin only) -->
      <Button
        v-if="authStore.canManageUsers"
        label="Manage Users"
        icon="pi pi-users"
        outlined
        size="small"
        class="w-full mb-2"
        @click="router.push('/users')"
      />

      <Button
        v-if="authStore.isAdmin || authStore.canManageUsers"
        label="History"
        icon="pi pi-history"
        outlined
        size="small"
        class="w-full mb-2"
        @click="showHistoryDialog = true"
        :badge="newHistoryCount > 0 ? newHistoryCount.toString() : undefined"
        badgeClass="p-badge-danger"
      />

      

      <Button
        label="Sign Out"
        icon="pi pi-sign-out"
        severity="danger"
        outlined
        size="small"
        class="w-full"
        @click="handleLogout"
      />
    </div>
    <Dialog v-model:visible="showAddBookDialog" header="Add Book" :style="{ width: '25vw' }">
      <InputText v-model="newBookTitle" placeholder="Book Title" class="w-full mb-2" />
      <InputText v-model="newBookDescription" placeholder="Description" class="w-full" />
      <template #footer>
        <Button label="Cancel" class="p-button-text" @click="showAddBookDialog = false" />
        <Button label="Save" :loading="store.actionLoading.addingBook" @click="saveBook" />
      </template>
    </Dialog>

    <Dialog v-model:visible="showHistoryDialog" header="Action History" :style="{ width: '50vw' }" modal>
      <div v-if="historyStore.loading" class="text-center p-4">
        <i class="pi pi-spin pi-spinner text-2xl"></i>
        <p>Loading history...</p>
      </div>
      <div v-else-if="historyStore.error" class="p-4 bg-red-100 text-red-700 rounded-lg">
        <strong>Error:</strong> {{ historyStore.error }}
      </div>
      <DataTable v-else :value="historyStore.history" stripedRows paginator :rows="10" :rowsPerPageOptions="[5, 10, 20, 50]" sortField="timestamp" :sortOrder="-1">
        <Column field="action" header="Action" sortable>
          <template #body="{ data }">
            <Tag :value="formatAction(data)" :severity="getActionTagSeverity(data.action)" />
          </template>
        </Column>
        <Column field="username" header="User" sortable>
          <template #body="slotProps">
            <span :class="{'font-bold text-primary': slotProps.data.userId === authStore.user?.id }">{{ slotProps.data.username }}</span>
          </template>
        </Column>
        <Column field="timestamp" header="Date" sortable>
          <template #body="slotProps">
            {{ new Date(slotProps.data.timestamp?.toDate()).toLocaleString() }}
          </template>
        </Column>
      </DataTable>
      <template #footer>
        <Button
          v-if="authStore.canManageUsers"
          label="Clear History"
          icon="pi pi-eraser"
          severity="danger"
          outlined
          class="w-full mt-3"
          @click="confirmClearHistory"
        />
      </template>
    </Dialog>

    <ConfirmDialog/>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useBookStore } from '../../stores/book';
import { useAuthStore } from '../../stores/auth';
import { useHistoryStore } from '../../stores/history';
import type { MenuItem } from 'primevue/menuitem';
import type { ActionType } from '../../types/actionHistory';
import Button from 'primevue/button';
import Menu from 'primevue/menu';
import Dialog from 'primevue/dialog';
import InputText from 'primevue/inputtext';
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import Tag from 'primevue/tag';
import ConfirmDialog from 'primevue/confirmdialog';
import { useConfirm } from "primevue/useconfirm";


const store = useBookStore();
const authStore = useAuthStore();
const historyStore = useHistoryStore();
const router = useRouter();
const route = useRoute();
const showAddBookDialog = ref(false);
const newBookTitle = ref('');
const newBookDescription = ref('');
const showHistoryDialog = ref(false);
const confirm = useConfirm();

const confirmClearHistory = () => {
  confirm.require({
    message: 'Are you sure you want to clear all history records? This action cannot be undone.',
    header: 'Clear History Confirmation',
    icon: 'pi pi-exclamation-triangle',
    acceptClass: 'p-button-danger',
    accept: async () => {
      await historyStore.clearAllHistory();
    },
    reject: () => {
      // Callback to execute when action is cancelled
    }
  });
};

const getActionTagSeverity = (action: ActionType) => {
  if (action.startsWith('create')) return 'success';
  if (action.startsWith('delete')) return 'danger';
  if (action.startsWith('update')) return 'info';
  return 'secondary';
};


const formatAction = (action: any) => {
  const { action: actionType, details } = action;
  switch (actionType) {
    case 'create-book':
      return `Created book: ${details.bookTitle}`;
    case 'delete-book':
      return `Deleted book: ${details.bookTitle}`;
    case 'create-chapter':
      return `Created chapter: ${details.chapterTitle}`;
    case 'delete-chapter':
      return `Deleted chapter: ${details.chapterTitle}`;
    case 'create-item':
      return `Created item: ${details.itemTitle}`;
    case 'update-item':
      return `Updated item: ${details.itemTitle}`;
    case 'delete-item':
      return `Deleted item: ${details.itemTitle}`;
    case 'download-qr':
      return `Downloaded QR code for: ${details.itemTitle}`;
    default:
      return actionType;
  }
};

const newHistoryCount = computed(() => {
  if (historyStore.newActionsAvailable) {
    const count = historyStore.history.length - historyStore.lastSeenCount;
    return count > 0 ? count : 0;
  }
  return 0;
});

watch(showHistoryDialog, (newValue) => {
  if (newValue) {
    historyStore.markHistoryAsSeen();
  }
});

onMounted(() => {
  if (authStore.isAdmin || authStore.canManageUsers) {
    historyStore.subscribeToHistory();
  }
});

onUnmounted(() => {
  historyStore.unsubscribeFromHistory();
});

// Function to handle book selection
const selectBook = (bookId: string) => {
  store.selectBook(bookId);
  router.push(`/book/${bookId}`);
};

const menuItems = computed<MenuItem[]>(() => {
  if (!Array.isArray(store.books)) {
    return [];
  }
  return [
    {
      label: 'Books',
      items: store.books.map(book => ({
        label: book.title,
        id: book.id,
      })),
    },
  ];
});

// Sync selectedBookId with route
watch(
  () => route.params.id,
  (id) => {
    if (typeof id === 'string') {
      store.selectBook(id);
    } else {
      store.selectBook(null);
    }
  },
  { immediate: true }
);

// Books are loaded automatically via Firestore real-time listeners in the store

const openAddBookDialog = () => {
  newBookTitle.value = '';
  newBookDescription.value = '';
  showAddBookDialog.value = true;
};

const saveBook = async () => {
  if (newBookTitle.value) {
    await store.addBook({
      title: newBookTitle.value,
      description: newBookDescription.value,
    });
    showAddBookDialog.value = false;
  }
};

const handleLogout = async () => {
  await authStore.logout();
  router.push('/auth/login');
};
</script>

<style scoped>
/* Custom styles if needed - most styling is handled by Tailwind */
</style>
