# Use Node.js 18 LTS
FROM node:18-alpine

# Install system dependencies for canvas and native modules
RUN apk add --no-cache \
    python3 \
    py3-pip \
    make \
    g++ \
    cairo-dev \
    jpeg-dev \
    pango-dev \
    musl-dev \
    giflib-dev \
    pixman-dev \
    pangomm-dev \
    libjpeg-turbo-dev \
    freetype-dev

# Set working directory
WORKDIR /app

# Copy package files first for better caching
COPY package*.json ./

# Install production dependencies only (faster)
RUN npm ci --omit=dev

# Copy server file and public assets (skip frontend source)
COPY server.cjs ./
COPY public ./public

# Note: Skipping frontend build for faster server-only deployment

# Expose port 8080 (Cloud Run default)
EXPOSE 8080

# Set environment variables
ENV NODE_ENV=production

# Start the server
CMD ["node", "server.cjs"]
