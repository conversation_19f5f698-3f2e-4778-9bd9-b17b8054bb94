<template>
  <div class="main-layout">
    <!-- Show loading overlay only if auth is not initialized -->
    <div v-if="!authStore.initialized" class="fixed inset-0 bg-surface-100 flex items-center justify-center z-50">
      <div class="text-center">
        <i class="pi pi-spin pi-spinner text-4xl text-primary mb-4"></i>
        <p class="text-surface-600">Initializing application...</p>
      </div>
    </div>

    <!-- Main interface - shows immediately when auth is ready -->
    <div v-else class="flex h-full w-full bg-surface-100 px-4 py-10">
      <div class="w-2/5 lg:w-1/4 xl:w-1/5 rounded-r-2xl">
        <Sidebar />
      </div>
      <div class="w-3/5 lg:w-3/4 xl:w-4/5 bg-surface-0 rounded-xl border border-surface-300 mx-6 p-6">
        <router-view />
      </div>
    </div>
    <Toast />
  </div>
</template>

<script setup lang="ts">
import Sidebar from './Sidebar.vue';
import Toast from 'primevue/toast';
import { useAuthStore } from '../../stores/auth';

const authStore = useAuthStore();
</script>

<style scoped>
.main-layout {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}
</style>
