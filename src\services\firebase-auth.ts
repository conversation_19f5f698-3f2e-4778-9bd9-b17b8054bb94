import {
  signInWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  type User as FirebaseUser
} from 'firebase/auth';
import {
  doc,
  getDoc,
  collection,
  query,
  getDocs,
  updateDoc
} from 'firebase/firestore';
import { auth, db } from '../config/firebase';
import { buildApiUrl } from '../config/api';
import type { User, UserRole } from '../types/user';

export interface CreateUserData {
  email: string;
  password: string;
  username: string;
  role: UserRole;
}

export interface UserProfile {
  uid: string;
  email: string;
  username: string;
  role: UserRole;
  createdAt: number;
  lastLogin?: number;
}

class FirebaseAuthService {
  // Create a new user account with role (server-side to avoid auto sign-in)
  async createUser(userData: CreateUserData): Promise<User> {
    try {
      // Use server-side API to create user without affecting current session
      const response = await fetch(buildApiUrl('/api/users'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${await auth.currentUser?.getIdToken()}`
        },
        body: JSON.stringify(userData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create user');
      }

      const user = await response.json();
      return user;
    } catch (error: any) {
      console.error('Error creating user:', error);
      throw new Error(error.message || 'Failed to create user');
    }
  }

  // Sign in user
  async signIn(email: string, password: string): Promise<User> {
    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      const firebaseUser = userCredential.user;
      
      // Get user profile from Firestore
      const userProfile = await this.getUserProfile(firebaseUser.uid);
      
      if (!userProfile) {
        throw new Error('User profile not found');
      }

      // Update last login
      await updateDoc(doc(db, 'users', firebaseUser.uid), {
        lastLogin: Date.now()
      });

      return {
        id: userProfile.uid,
        username: userProfile.username,
        email: userProfile.email,
        role: userProfile.role,
        createdAt: userProfile.createdAt,
        lastLogin: Date.now()
      };
    } catch (error: any) {
      console.error('Error signing in:', error);
      throw new Error(error.message || 'Failed to sign in');
    }
  }

  // Sign out user
  async signOut(): Promise<void> {
    try {
      await signOut(auth);
    } catch (error: any) {
      console.error('Error signing out:', error);
      throw new Error(error.message || 'Failed to sign out');
    }
  }

  // Get user profile from Firestore
  async getUserProfile(uid: string): Promise<UserProfile | null> {
    try {
      const userDoc = await getDoc(doc(db, 'users', uid));
      if (userDoc.exists()) {
        return userDoc.data() as UserProfile;
      }
      return null;
    } catch (error: any) {
      console.error('Error getting user profile:', error);
      return null;
    }
  }

  // Get all users (admin only)
  async getAllUsers(): Promise<UserProfile[]> {
    try {
      const usersQuery = query(collection(db, 'users'));
      const querySnapshot = await getDocs(usersQuery);
      
      const users: UserProfile[] = [];
      querySnapshot.forEach((doc) => {
        users.push(doc.data() as UserProfile);
      });
      
      return users;
    } catch (error: any) {
      console.error('Error getting all users:', error);
      throw new Error(error.message || 'Failed to get users');
    }
  }

  // Update user role (admin only)
  async updateUserRole(uid: string, newRole: UserRole): Promise<void> {
    try {
      await updateDoc(doc(db, 'users', uid), {
        role: newRole
      });
    } catch (error: any) {
      console.error('Error updating user role:', error);
      throw new Error(error.message || 'Failed to update user role');
    }
  }

  // Delete user (admin only)
  async deleteUser(uid: string): Promise<void> {
    try {
      // Use server-side API to delete both Firebase Auth user and Firestore document
      const response = await fetch(buildApiUrl(`/api/users/${uid}`), {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${await auth.currentUser?.getIdToken()}`
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete user');
      }
    } catch (error: any) {
      console.error('Error deleting user:', error);
      throw new Error(error.message || 'Failed to delete user');
    }
  }

  // Listen to auth state changes
  onAuthStateChanged(callback: (user: FirebaseUser | null) => void) {
    return onAuthStateChanged(auth, callback);
  }

  // Get current Firebase user
  getCurrentUser(): FirebaseUser | null {
    return auth.currentUser;
  }
}

export const firebaseAuthService = new FirebaseAuthService();
