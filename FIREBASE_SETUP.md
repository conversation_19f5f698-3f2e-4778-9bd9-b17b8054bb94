# Firebase Authentication Setup

This guide will help you set up Firebase authentication for the textbook platform.

## Prerequisites

1. Firebase project created
2. Environment variables configured in `.env`
3. Firebase SDK installed

## Setup Steps

### 1. Create Initial Admin User

Run the setup script to create your first admin user:

```bash
node setup-admin.js
```

**Important:** 
- Change the email and password in `setup-admin.js` before running
- Use a secure password
- Delete or secure this script after use

### 2. Firebase Console Configuration

1. Go to [Firebase Console](https://console.firebase.google.com)
2. Select your project
3. Navigate to **Authentication** > **Sign-in method**
4. Enable **Email/Password** authentication
5. Navigate to **Firestore Database**
6. Create database in production mode
7. Set up security rules (see below)

### 3. Firestore Security Rules

Replace the default rules with:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read their own profile
    match /users/{userId} {
      allow read: if request.auth != null && request.auth.uid == userId;
      // Only admins can write user profiles
      allow write: if request.auth != null && 
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    // Admin-only access to all user documents for user management
    match /users/{document=**} {
      allow read, write: if request.auth != null && 
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
  }
}
```

### 4. Environment Variables

Ensure your `.env` file contains:

```env
VITE_API_KEY=your_api_key
VITE_AUTH_DOMAIN=your_project.firebaseapp.com
VITE_PROJECT_ID=your_project_id
VITE_STORAGE_BUCKET=your_project.firebasestorage.app
VITE_MESSAGING_SENDER_ID=your_sender_id
VITE_APP_ID=your_app_id
```

### 5. Backend Integration (Optional)

If you have a backend API, update it to verify Firebase ID tokens:

```javascript
const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
admin.initializeApp({
  credential: admin.credential.applicationDefault(),
  projectId: 'your-project-id'
});

// Middleware to verify Firebase tokens
async function verifyFirebaseToken(req, res, next) {
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ error: 'No token provided' });
  }
  
  const token = authHeader.split('Bearer ')[1];
  
  try {
    const decodedToken = await admin.auth().verifyIdToken(token);
    req.user = decodedToken;
    next();
  } catch (error) {
    return res.status(401).json({ error: 'Invalid token' });
  }
}
```

## User Roles

The system supports three roles:

- **Viewer**: Can view content only
- **Editor**: Can create and edit content
- **Admin**: Full access including user management

## Features

### User Management (Admin Only)

- Create new users with email/password
- Assign roles to users
- View all users and their details
- Delete users (except self)

### Authentication Flow

1. Users sign in with email and password
2. Firebase authenticates the user
3. User profile is loaded from Firestore
4. Permissions are set based on role
5. Firebase ID token is used for API calls

## Troubleshooting

### Common Issues

1. **"User profile not found"**
   - Ensure Firestore has the user document
   - Check security rules allow reading user profiles

2. **"Permission denied"**
   - Verify Firestore security rules
   - Check user role in Firestore document

3. **"Invalid token"**
   - Ensure Firebase ID token is being sent correctly
   - Check token expiration

### Development Tips

1. Use Firebase Emulator Suite for local development
2. Check browser console for detailed error messages
3. Monitor Firestore usage in Firebase Console
4. Test with different user roles

## Security Considerations

1. **Never expose Firebase config secrets**
2. **Use proper Firestore security rules**
3. **Validate user roles on both client and server**
4. **Regularly rotate API keys**
5. **Monitor authentication logs**

## Next Steps

1. Test the authentication flow
2. Create additional users through the admin interface
3. Set up proper backup for Firestore data
4. Configure monitoring and alerts
5. Set up proper deployment pipeline
