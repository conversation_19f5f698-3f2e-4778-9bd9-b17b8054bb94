@echo off
REM Check deployment status for textbook-platform

set SERVICE_NAME=my-textbook-app
set REGION=us-central1
set SERVICE_URL=https://my-textbook-app-376480732351.us-central1.run.app

echo 🔍 Checking deployment status...
echo.

REM Check service status
echo 📊 Service Status:
gcloud run services describe %SERVICE_NAME% --region=%REGION% --format="value(status.conditions[0].status)"
echo.

REM Check latest revision
echo 📋 Latest Revision:
gcloud run revisions list --service=%SERVICE_NAME% --region=%REGION% --limit=1
echo.

REM Test the new endpoint
echo 🧪 Testing new /api/user/profile endpoint:
curl -s "%SERVICE_URL%/api/user/profile" -H "Authorization: Bearer test" | findstr "profile\|error\|Cannot\|Missing" >nul
if %ERRORLEVEL% EQU 0 (
    echo ✅ New endpoint is responding!
    echo Response:
    curl -s "%SERVICE_URL%/api/user/profile" -H "Authorization: Bearer test"
) else (
    echo ❌ Endpoint not responding correctly
    echo Response:
    curl -s "%SERVICE_URL%/api/user/profile" -H "Authorization: Bearer test"
)
echo.

REM Test a known working endpoint
echo 🧪 Testing existing /api/books endpoint:
curl -s "%SERVICE_URL%/api/books" -H "Authorization: Bearer test" | findstr "error\|Missing" >nul
if %ERRORLEVEL% EQU 0 (
    echo ✅ Server is responding!
) else (
    echo ❌ Server might be down
)

echo.
echo 🌐 Service URL: %SERVICE_URL%
echo 📋 Console: https://console.cloud.google.com/run
