export interface Option {
  text: string;
}

export interface TestQuestion {
  id: string;
  question: string;
  options: Option[];
  correctAnswer: number;
}

export interface Item {
  id: string;
  title: string;
  type: 'question' | 'text' | 'image' | 'link' | 'map' | 'diagram' | 'timed-question' | 'test';
  question?: string;
  options?: Option[];
  correctAnswer?: number;
  content?: string;
  url?: string;
  // Fields for timed questions
  timedAnswer?: string;
  revealTimeSeconds?: number;
  // Fields for test items
  testQuestions?: TestQuestion[];
}