import { createApp } from 'vue'
import './styles/main.css'
import App from './App.vue'
import router from './router'
import { createPinia } from 'pinia'
import PrimeVue from 'primevue/config'
import Aura from '@primeuix/themes/aura'
import Tooltip from 'primevue/tooltip'
import ToastService from 'primevue/toastservice'
import ConfirmationService from 'primevue/confirmationservice'
import { useAuthStore } from './stores/auth'
import { useBookStore } from './stores/book'

const app = createApp(App)
const pinia = createPinia()
app.use(pinia)
app.use(router)
app.use(PrimeVue, {
    theme: {
        preset: Aura,
        options: {
            darkModeSelector: false || 'none'
        }
    }
})
app.use(ToastService)
app.use(ConfirmationService)
app.directive('tooltip', Tooltip)

// Mount the app immediately for fast UI rendering
app.mount('#app')

// Initialize stores after mounting for non-blocking startup
const authStore = useAuthStore()
const bookStore = useBookStore()

// Initialize Firebase auth (non-blocking)
authStore.initializeAuth()

// Initialize book store immediately (will handle auth state internally)
bookStore.initializeStore()
