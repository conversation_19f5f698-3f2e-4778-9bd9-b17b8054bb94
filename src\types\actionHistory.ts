export type ActionType =
  | 'create-book'
  | 'delete-book'
  | 'create-chapter'
  | 'delete-chapter'
  | 'create-item'
  | 'update-item'
  | 'delete-item'
  | 'download-qr';

export interface ActionHistory {
  id: string;
  action: ActionType;
  userId: string;
  username: string;
  timestamp: any; // Firestore server timestamp
  details: Record<string, any>; // e.g., { bookId: '...', bookTitle: '...' }
}
